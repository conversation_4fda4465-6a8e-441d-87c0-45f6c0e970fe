"""
场景参数模块
提供雷达场景参数的配置、管理和应用功能
"""

from flask import Blueprint, request, jsonify, current_app
from functools import wraps
import jwt
from datetime import datetime
from bson import ObjectId

# 创建蓝图
scene_parameter = Blueprint("scene_parameter", __name__)


# JWT认证装饰器
def token_required(f):
    @wraps(f)
    def decorated(*args, **kwargs):
        token = None
        if "Authorization" in request.headers:
            token = request.headers["Authorization"].split(" ")[1]

        if not token:
            return jsonify({"message": "缺少认证令牌！"}), 401

        try:
            data = jwt.decode(
                token, current_app.config["SECRET_KEY"], algorithms=["HS256"]
            )
            current_user = current_app.mongo.db.users.find_one(
                {"username": data["username"]}
            )
        except:
            return jsonify({"message": "令牌无效！"}), 401

        return f(current_user, *args, **kwargs)

    return decorated


# 获取场景列表
@scene_parameter.route("/scenes", methods=["GET"])
@token_required
def get_scenes(current_user):
    try:
        scenes = list(current_app.mongo.db.scenes.find())

        # 转换ObjectId为字符串
        for scene in scenes:
            scene["_id"] = str(scene["_id"])

        return jsonify(scenes)
    except:
        return jsonify({"message": "获取场景列表失败！"}), 500


# 获取单个场景详情
@scene_parameter.route("/scenes/<scene_id>", methods=["GET"])
@token_required
def get_scene(current_user, scene_id):
    try:
        scene = current_app.mongo.db.scenes.find_one({"_id": ObjectId(scene_id)})

        if not scene:
            return jsonify({"message": "场景不存在！"}), 404

        # 转换ObjectId为字符串
        scene["_id"] = str(scene["_id"])

        return jsonify(scene)
    except:
        return jsonify({"message": "获取场景详情失败！"}), 500


# 创建新场景
@scene_parameter.route("/scenes", methods=["POST"])
@token_required
def create_scene(current_user):
    try:
        data = request.json

        if not data:
            return jsonify({"message": "没有提供场景信息！"}), 400

        # 验证必要字段
        required_fields = ["name", "description", "parameters"]
        for field in required_fields:
            if field not in data:
                return jsonify({"message": f"缺少必要字段: {field}"}), 400

        # 创建新场景
        new_scene = {
            "name": data["name"],
            "description": data["description"],
            "parameters": data["parameters"],
            "created_by": current_user.get("username"),
            "created_at": datetime.utcnow(),
            "is_active": data.get("is_active", True),
        }

        result = current_app.mongo.db.scenes.insert_one(new_scene)

        return (
            jsonify({"message": "场景创建成功！", "scene_id": str(result.inserted_id)}),
            201,
        )
    except:
        return jsonify({"message": "创建场景失败！"}), 500


# 更新场景
@scene_parameter.route("/scenes/<scene_id>", methods=["PUT"])
@token_required
def update_scene(current_user, scene_id):
    try:
        data = request.json

        if not data:
            return jsonify({"message": "没有提供更新信息！"}), 400

        # 更新场景
        update_data = {
            **data,
            "updated_by": current_user.get("username"),
            "updated_at": datetime.utcnow(),
        }

        result = current_app.mongo.db.scenes.update_one(
            {"_id": ObjectId(scene_id)}, {"$set": update_data}
        )

        if result.matched_count == 0:
            return jsonify({"message": "场景不存在！"}), 404

        return jsonify({"message": "场景更新成功！"})
    except:
        return jsonify({"message": "更新场景失败！"}), 500


# 删除场景
@scene_parameter.route("/scenes/<scene_id>", methods=["DELETE"])
@token_required
def delete_scene(current_user, scene_id):
    try:
        result = current_app.mongo.db.scenes.delete_one({"_id": ObjectId(scene_id)})

        if result.deleted_count == 0:
            return jsonify({"message": "场景不存在！"}), 404

        return jsonify({"message": "场景删除成功！"})
    except:
        return jsonify({"message": "删除场景失败！"}), 500


# 应用场景参数到雷达
@scene_parameter.route("/scenes/<scene_id>/apply/<radar_id>", methods=["POST"])
@token_required
def apply_scene(current_user, scene_id, radar_id):
    try:
        # 获取场景参数
        scene = current_app.mongo.db.scenes.find_one({"_id": ObjectId(scene_id)})

        if not scene:
            return jsonify({"message": "场景不存在！"}), 404

        # 检查雷达是否存在
        radar = current_app.mongo.db.radars.find_one({"_id": ObjectId(radar_id)})

        if not radar:
            return jsonify({"message": "雷达设备不存在！"}), 404

        # 应用场景参数到雷达
        result = current_app.mongo.db.radars.update_one(
            {"_id": ObjectId(radar_id)},
            {
                "$set": {
                    "parameters": scene["parameters"],
                    "current_scene": scene_id,
                    "scene_applied_at": datetime.utcnow(),
                    "scene_applied_by": current_user.get("username"),
                }
            },
        )

        # 记录应用场景的操作
        application_log = {
            "radar_id": ObjectId(radar_id),
            "scene_id": ObjectId(scene_id),
            "applied_by": current_user.get("username"),
            "timestamp": datetime.utcnow(),
            "parameters": scene["parameters"],
        }

        current_app.mongo.db.scene_applications.insert_one(application_log)

        return jsonify({"message": "场景参数应用成功！"})
    except:
        return jsonify({"message": "应用场景参数失败！"}), 500


# 获取场景应用历史
@scene_parameter.route("/scenes/history/<radar_id>", methods=["GET"])
@token_required
def get_scene_history(current_user, radar_id):
    try:
        # 获取分页参数
        page = int(request.args.get("page", 1))
        per_page = int(request.args.get("per_page", 10))

        # 计算跳过的记录数
        skip = (page - 1) * per_page

        # 获取应用历史
        history = list(
            current_app.mongo.db.scene_applications.find(
                {"radar_id": ObjectId(radar_id)},
                sort=[("timestamp", -1)],
                skip=skip,
                limit=per_page,
            )
        )

        # 转换ObjectId为字符串
        for record in history:
            record["_id"] = str(record["_id"])
            record["radar_id"] = str(record["radar_id"])
            record["scene_id"] = str(record["scene_id"])

        # 获取总记录数
        total_records = current_app.mongo.db.scene_applications.count_documents(
            {"radar_id": ObjectId(radar_id)}
        )

        return jsonify(
            {
                "history": history,
                "pagination": {
                    "page": page,
                    "per_page": per_page,
                    "total_records": total_records,
                    "total_pages": (total_records + per_page - 1) // per_page,
                },
            }
        )
    except:
        return jsonify({"message": "获取场景应用历史失败！"}), 500


# 获取场景参数模板
@scene_parameter.route("/templates", methods=["GET"])
@token_required
def get_scene_templates(current_user):
    try:
        templates = list(current_app.mongo.db.scene_templates.find())

        # 转换ObjectId为字符串
        for template in templates:
            template["_id"] = str(template["_id"])

        return jsonify(templates)
    except:
        return jsonify({"message": "获取场景模板失败！"}), 500


# 创建场景参数模板
@scene_parameter.route("/templates", methods=["POST"])
@token_required
def create_scene_template(current_user):
    try:
        data = request.json

        if not data:
            return jsonify({"message": "没有提供模板信息！"}), 400

        # 验证必要字段
        required_fields = ["name", "description", "parameters"]
        for field in required_fields:
            if field not in data:
                return jsonify({"message": f"缺少必要字段: {field}"}), 400

        # 创建新模板
        new_template = {
            "name": data["name"],
            "description": data["description"],
            "parameters": data["parameters"],
            "created_by": current_user.get("username"),
            "created_at": datetime.utcnow(),
        }

        result = current_app.mongo.db.scene_templates.insert_one(new_template)

        return (
            jsonify(
                {
                    "message": "场景模板创建成功！",
                    "template_id": str(result.inserted_id),
                }
            ),
            201,
        )
    except:
        return jsonify({"message": "创建场景模板失败！"}), 500


# 错误处理
@scene_parameter.errorhandler(404)
def not_found(error):
    return jsonify({"message": "资源不存在！"}), 404


@scene_parameter.errorhandler(500)
def internal_error(error):
    return jsonify({"message": "服务器内部错误！"}), 500
