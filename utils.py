from flask import jsonify, Response
from typing import Dict, Any, Optional, Callable, Union, Tuple, TypeVar
from functools import wraps
import logging

from pymongo.errors import (
    ConnectionFailure,
    OperationFailure,
)
from bson.errors import InvalidDocument
from type import *

# Generic type for decorated functions
F = TypeVar("F", bound=Callable[..., Any])

logger = logging.getLogger(__name__)


def handle_api_exceptions(info: str = "") -> Callable[[F], F]:
    """
    API异常处理装饰器工厂
    用于统一处理常见的API相关异常

    Args:
        info: 异常信息前缀

    Returns:
        装饰器函数
    """

    def decorator(f: F) -> F:
        @wraps(f)
        def decorated_function(
            *args: Any, **kwargs: Any
        ) -> Union[Any, Tuple[Response, int]]:
            try:
                return f(*args, **kwargs)
            except Exception as e:
                logger.error(f"{info}失败: {str(e)}", exc_info=True)
                return (
                    jsonify(
                        {
                            "status": "error",
                            "message": "服务器内部错误" + str(e),
                        }
                    ),
                    500,
                )

        return decorated_function  # type: ignore[return-value]

    return decorator


def handle_database_exceptions(f: F) -> F:
    """
    数据库异常处理装饰器
    用于统一处理常见的数据库相关异常

    Args:
        f: 被装饰的函数

    Returns:
        装饰后的函数
    """

    @wraps(f)
    def decorated_function(*args: Any, **kwargs: Any) -> ApiResponse:
        try:
            return f(*args, **kwargs)
        except ConnectionFailure as e:
            logger.error(f"数据库连接失败: {str(e)}")
            return (
                jsonify(
                    {
                        "status": "error",
                        "message": "数据库连接失败" + str(e),
                    }
                ),
                503,
            )
        except OperationFailure as e:
            logger.error(f"数据库操作失败: {str(e)}")
            return (
                jsonify(
                    {
                        "status": "error",
                        "message": "数据库操作失败" + str(e),
                    }
                ),
                500,
            )
        except InvalidDocument as e:
            logger.error(f"无效文档格式: {str(e)}")
            return (
                jsonify(
                    {
                        "status": "error",
                        "message": "数据格式错误" + str(e),
                    }
                ),
                400,
            )
        except ValueError as e:
            logger.error(f"参数值错误: {str(e)}")
            return (
                jsonify(
                    {
                        "status": "error",
                        "message": "参数格式错误" + str(e),
                    }
                ),
                400,
            )

    return decorated_function  # type: ignore[return-value]


class ApiValidator:
    """API请求验证器"""

    @staticmethod
    def validate_request_data(
        data: Optional[Dict[str, Any]],
    ) -> Optional[Tuple[Response, int]]:
        """
        验证请求数据

        Args:
            data: 请求数据

        Returns:
            验证失败时返回错误响应，成功时返回None
        """
        if not data:
            return jsonify({"error": "请求数据为空", "code": "INVALID_REQUEST"}), 400
        return None

    @staticmethod
    def validate_id(
        id: Optional[str], id_name: str = ""
    ) -> Optional[Tuple[Response, int]]:
        """
        验证ID参数的有效性

        静态方法，用于验证传入的ID参数是否有效。如果ID为空或空白字符串，
        返回包含错误信息和状态码的响应元组；否则返回None表示验证通过。

        Args:
            id (Optional[str]): 待验证的ID字符串，可为None
            id_name (str): ID参数的名称，用于错误信息中显示

        Returns:
            Optional[Tuple[Response, int]]:
                如果验证失败，返回(错误响应, 400状态码)；
                如果验证通过，返回None
        """
        id_name_upper: str = id_name.upper()
        if not id:
            return (
                jsonify(
                    {"error": f"{id_name}参数缺失", "code": f"MISSING_{id_name_upper}"}
                ),
                400,
            )

        if not id.strip():
            return (
                jsonify(
                    {"error": f"{id_name}不能为空", "code": f"INVALID_{id_name_upper}"}
                ),
                400,
            )

        return None
