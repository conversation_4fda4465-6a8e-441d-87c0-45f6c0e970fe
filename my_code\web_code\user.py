"""
用户管理模块
提供用户认证、注册、信息管理等功能
"""

from flask import Blueprint, request, jsonify, current_app
from functools import wraps
import jwt
from datetime import datetime, timedelta
import hashlib

# 创建蓝图
user = Blueprint("user", __name__)


# JWT认证装饰器
def token_required(f):
    @wraps(f)
    def decorated(*args, **kwargs):
        token = None
        if "Authorization" in request.headers:
            token = request.headers["Authorization"].split(" ")[1]

        if not token:
            return jsonify({"message": "缺少认证令牌！"}), 401

        try:
            data = jwt.decode(
                token, current_app.config["SECRET_KEY"], algorithms=["HS256"]
            )
            current_user = current_app.mongo.db.users.find_one(
                {"username": data["username"]}
            )
        except:
            return jsonify({"message": "令牌无效！"}), 401

        return f(current_user, *args, **kwargs)

    return decorated


# 用户登录
@user.route("/login", methods=["POST"])
def login():
    auth = request.json

    if not auth or not auth.get("username") or not auth.get("password"):
        return jsonify({"message": "登录失败，缺少认证信息！"}), 401

    user_data = current_app.mongo.db.users.find_one({"username": auth.get("username")})

    if not user_data:
        return jsonify({"message": "用户不存在！"}), 401

    # 密码哈希比较
    password_hash = hashlib.sha256(auth.get("password").encode()).hexdigest()
    if password_hash == user_data.get("password"):
        token = jwt.encode(
            {
                "username": user_data.get("username"),
                "exp": datetime.utcnow() + timedelta(hours=24),
            },
            current_app.config["SECRET_KEY"],
            algorithm="HS256",
        )

        return jsonify({"token": token})

    return jsonify({"message": "密码错误！"}), 401


# 用户注册
@user.route("/register", methods=["POST"])
def register():
    data = request.json

    if not data or not data.get("username") or not data.get("password"):
        return jsonify({"message": "注册失败，缺少必要信息！"}), 400

    # 检查用户是否已存在
    existing_user = current_app.mongo.db.users.find_one(
        {"username": data.get("username")}
    )
    if existing_user:
        return jsonify({"message": "用户名已存在！"}), 400

    # 密码哈希
    password_hash = hashlib.sha256(data.get("password").encode()).hexdigest()

    # 创建新用户
    new_user = {
        "username": data.get("username"),
        "password": password_hash,
        "email": data.get("email", ""),
        "role": data.get("role", "user"),
        "created_at": datetime.utcnow(),
    }

    current_app.mongo.db.users.insert_one(new_user)

    return jsonify({"message": "用户注册成功！"}), 201


# 获取用户信息
@user.route("/profile", methods=["GET"])
@token_required
def get_profile(current_user):
    user_data = {
        "username": current_user.get("username"),
        "email": current_user.get("email", ""),
        "role": current_user.get("role", "user"),
        "created_at": current_user.get("created_at"),
    }

    return jsonify(user_data)


# 更新用户信息
@user.route("/profile", methods=["PUT"])
@token_required
def update_profile(current_user):
    data = request.json

    updates = {}
    if data.get("email"):
        updates["email"] = data.get("email")

    if data.get("password"):
        updates["password"] = hashlib.sha256(data.get("password").encode()).hexdigest()

    if updates:
        current_app.mongo.db.users.update_one(
            {"username": current_user.get("username")}, {"$set": updates}
        )

        return jsonify({"message": "用户信息更新成功！"})

    return jsonify({"message": "没有提供需要更新的信息！"}), 400


# 错误处理
@user.errorhandler(404)
def not_found(error):
    return jsonify({"message": "资源不存在！"}), 404


@user.errorhandler(500)
def internal_error(error):
    return jsonify({"message": "服务器内部错误！"}), 500
