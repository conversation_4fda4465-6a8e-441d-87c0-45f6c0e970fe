"""
雷达管理模块
提供雷达设备的添加、删除、修改、查询等功能
"""

from flask import Blueprint, request, jsonify, current_app
from functools import wraps
import jwt
from datetime import datetime
from bson import ObjectId

# 创建蓝图
radar_manage = Blueprint("radar_manage", __name__)


# JWT认证装饰器
def token_required(f):
    @wraps(f)
    def decorated(*args, **kwargs):
        token = None
        if "Authorization" in request.headers:
            token = request.headers["Authorization"].split(" ")[1]

        if not token:
            return jsonify({"message": "缺少认证令牌！"}), 401

        try:
            data = jwt.decode(
                token, current_app.config["SECRET_KEY"], algorithms=["HS256"]
            )
            current_user = current_app.mongo.db.users.find_one(
                {"username": data["username"]}
            )
        except:
            return jsonify({"message": "令牌无效！"}), 401

        return f(current_user, *args, **kwargs)

    return decorated


# 获取所有雷达设备
@radar_manage.route("/radars", methods=["GET"])
@token_required
def get_all_radars(current_user):
    radars = list(current_app.mongo.db.radars.find())

    # 转换ObjectId为字符串
    for radar in radars:
        radar["_id"] = str(radar["_id"])

    return jsonify(radars)


# 获取单个雷达设备
@radar_manage.route("/radars/<radar_id>", methods=["GET"])
@token_required
def get_radar(current_user, radar_id):
    try:
        radar = current_app.mongo.db.radars.find_one({"_id": ObjectId(radar_id)})
        if radar:
            radar["_id"] = str(radar["_id"])
            return jsonify(radar)
        return jsonify({"message": "雷达设备不存在！"}), 404
    except:
        return jsonify({"message": "无效的雷达ID！"}), 400


# 添加雷达设备
@radar_manage.route("/radars", methods=["POST"])
@token_required
def add_radar(current_user):
    # 检查用户权限
    if current_user.get("role") != "admin":
        return jsonify({"message": "权限不足，无法添加雷达设备！"}), 403

    data = request.json

    if not data:
        return jsonify({"message": "没有提供雷达信息！"}), 400

    # 验证必要字段
    required_fields = ["name", "model", "location"]
    for field in required_fields:
        if field not in data:
            return jsonify({"message": f"缺少必要字段: {field}"}), 400

    # 创建新雷达记录
    new_radar = {
        "name": data.get("name"),
        "model": data.get("model"),
        "location": data.get("location"),
        "status": data.get("status", "inactive"),
        "parameters": data.get("parameters", {}),
        "created_at": datetime.utcnow(),
        "created_by": current_user.get("username"),
    }

    result = current_app.mongo.db.radars.insert_one(new_radar)

    return (
        jsonify({"message": "雷达设备添加成功！", "radar_id": str(result.inserted_id)}),
        201,
    )


# 更新雷达设备
@radar_manage.route("/radars/<radar_id>", methods=["PUT"])
@token_required
def update_radar(current_user, radar_id):
    # 检查用户权限
    if current_user.get("role") != "admin":
        return jsonify({"message": "权限不足，无法更新雷达设备！"}), 403

    data = request.json

    if not data:
        return jsonify({"message": "没有提供更新信息！"}), 400

    try:
        # 更新雷达记录
        result = current_app.mongo.db.radars.update_one(
            {"_id": ObjectId(radar_id)},
            {
                "$set": {
                    **data,
                    "updated_at": datetime.utcnow(),
                    "updated_by": current_user.get("username"),
                }
            },
        )

        if result.matched_count == 0:
            return jsonify({"message": "雷达设备不存在！"}), 404

        return jsonify({"message": "雷达设备更新成功！"})
    except:
        return jsonify({"message": "无效的雷达ID！"}), 400


# 删除雷达设备
@radar_manage.route("/radars/<radar_id>", methods=["DELETE"])
@token_required
def delete_radar(current_user, radar_id):
    # 检查用户权限
    if current_user.get("role") != "admin":
        return jsonify({"message": "权限不足，无法删除雷达设备！"}), 403

    try:
        result = current_app.mongo.db.radars.delete_one({"_id": ObjectId(radar_id)})

        if result.deleted_count == 0:
            return jsonify({"message": "雷达设备不存在！"}), 404

        return jsonify({"message": "雷达设备删除成功！"})
    except:
        return jsonify({"message": "无效的雷达ID！"}), 400


# 雷达状态控制
@radar_manage.route("/radars/<radar_id>/status", methods=["PUT"])
@token_required
def update_radar_status(current_user, radar_id):
    data = request.json

    if not data or "status" not in data:
        return jsonify({"message": "没有提供状态信息！"}), 400

    valid_statuses = ["active", "inactive", "maintenance", "error"]
    if data.get("status") not in valid_statuses:
        return (
            jsonify(
                {"message": f'无效的状态值！有效值为: {", ".join(valid_statuses)}'}
            ),
            400,
        )

    try:
        result = current_app.mongo.db.radars.update_one(
            {"_id": ObjectId(radar_id)},
            {
                "$set": {
                    "status": data.get("status"),
                    "status_updated_at": datetime.utcnow(),
                    "status_updated_by": current_user.get("username"),
                }
            },
        )

        if result.matched_count == 0:
            return jsonify({"message": "雷达设备不存在！"}), 404

        return jsonify({"message": "雷达状态更新成功！"})
    except:
        return jsonify({"message": "无效的雷达ID！"}), 400


# 错误处理
@radar_manage.errorhandler(404)
def not_found(error):
    return jsonify({"message": "资源不存在！"}), 404


@radar_manage.errorhandler(500)
def internal_error(error):
    return jsonify({"message": "服务器内部错误！"}), 500
