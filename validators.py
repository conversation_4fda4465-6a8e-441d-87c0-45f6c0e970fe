"""
高级验证和存在性检查框架
提供链式验证、自动错误处理和简化的API
"""

from flask import jsonify, request
from typing import Any, Dict, List, Optional, Union, Callable, TypeVar, Generic
from pymongo.collection import Collection
from functools import wraps
import logging

logger = logging.getLogger(__name__)

# 类型定义
T = TypeVar('T')
ApiResponse = Union[Dict[str, Any], tuple[Dict[str, Any], int]]


class ValidationError(Exception):
    """验证错误异常"""
    def __init__(self, message: str, code: str = "VALIDATION_ERROR", status_code: int = 400):
        self.message = message
        self.code = code
        self.status_code = status_code
        super().__init__(message)

    def to_response(self) -> tuple[Dict[str, Any], int]:
        """转换为Flask响应"""
        return jsonify({"error": self.message, "code": self.code}), self.status_code


class ValidationResult(Generic[T]):
    """验证结果包装器"""
    def __init__(self, value: Optional[T] = None, error: Optional[ValidationError] = None):
        self.value = value
        self.error = error
        self.is_valid = error is None

    def unwrap(self) -> T:
        """获取值，如果有错误则抛出异常"""
        if self.error:
            raise self.error
        return self.value

    def unwrap_or(self, default: T) -> T:
        """获取值，如果有错误则返回默认值"""
        return self.value if self.is_valid else default


class ChainValidator:
    """链式验证器 - 支持流畅的验证链"""
    
    def __init__(self, value: Any = None, context: str = ""):
        self.value = value
        self.context = context
        self.errors: List[ValidationError] = []

    def required(self, message: Optional[str] = None) -> 'ChainValidator':
        """检查值是否存在"""
        if self.value is None:
            error_msg = message or f"{self.context}不能为空"
            self.errors.append(ValidationError(error_msg, "REQUIRED_FIELD_MISSING"))
        return self

    def not_empty(self, message: Optional[str] = None) -> 'ChainValidator':
        """检查值是否非空"""
        if self.value is not None and (
            (isinstance(self.value, str) and not self.value.strip()) or
            (isinstance(self.value, (list, dict)) and len(self.value) == 0)
        ):
            error_msg = message or f"{self.context}不能为空"
            self.errors.append(ValidationError(error_msg, "EMPTY_VALUE"))
        return self

    def min_length(self, length: int, message: Optional[str] = None) -> 'ChainValidator':
        """检查最小长度"""
        if self.value is not None and hasattr(self.value, '__len__') and len(self.value) < length:
            error_msg = message or f"{self.context}长度不能少于{length}"
            self.errors.append(ValidationError(error_msg, "MIN_LENGTH_ERROR"))
        return self

    def max_length(self, length: int, message: Optional[str] = None) -> 'ChainValidator':
        """检查最大长度"""
        if self.value is not None and hasattr(self.value, '__len__') and len(self.value) > length:
            error_msg = message or f"{self.context}长度不能超过{length}"
            self.errors.append(ValidationError(error_msg, "MAX_LENGTH_ERROR"))
        return self

    def custom(self, validator_func: Callable[[Any], bool], message: str, code: str = "CUSTOM_VALIDATION_ERROR") -> 'ChainValidator':
        """自定义验证函数"""
        if self.value is not None and not validator_func(self.value):
            self.errors.append(ValidationError(message, code))
        return self

    def result(self) -> ValidationResult[Any]:
        """获取验证结果"""
        if self.errors:
            return ValidationResult(error=self.errors[0])
        return ValidationResult(value=self.value)

    def raise_if_invalid(self) -> Any:
        """如果验证失败则抛出异常，否则返回值"""
        result = self.result()
        if not result.is_valid:
            raise result.error
        return result.value


class DatabaseHelper:
    """数据库查询助手 - 自动处理存在性检查"""
    
    def __init__(self, collection: Collection):
        self.collection = collection

    def find_one_or_fail(self, query: Dict[str, Any], 
                        error_message: str = "记录不存在", 
                        error_code: str = "RECORD_NOT_FOUND") -> Dict[str, Any]:
        """查找单个文档，如果不存在则抛出异常"""
        doc = self.collection.find_one(query)
        if doc is None:
            raise ValidationError(error_message, error_code, 404)
        return doc

    def find_field_or_fail(self, query: Dict[str, Any], field: str,
                          error_message: Optional[str] = None,
                          error_code: str = "FIELD_NOT_FOUND") -> Any:
        """查找文档中的特定字段，如果不存在则抛出异常"""
        doc = self.find_one_or_fail(query)
        value = doc.get(field)
        if value is None:
            error_msg = error_message or f"字段 {field} 不存在"
            raise ValidationError(error_msg, error_code, 404)
        return value

    def find_non_empty_field(self, query: Dict[str, Any], field: str,
                           error_message: Optional[str] = None,
                           error_code: str = "FIELD_EMPTY") -> Any:
        """查找文档中的非空字段"""
        value = self.find_field_or_fail(query, field, error_message, error_code)
        if isinstance(value, (list, dict)) and len(value) == 0:
            error_msg = error_message or f"字段 {field} 为空"
            raise ValidationError(error_msg, error_code, 404)
        return value


class RequestValidator:
    """请求验证器 - 简化请求数据验证"""
    
    @staticmethod
    def get_json_or_fail() -> Dict[str, Any]:
        """获取JSON数据，如果失败则抛出异常"""
        data = request.get_json()
        if not data:
            raise ValidationError("请求数据为空", "INVALID_REQUEST")
        return data

    @staticmethod
    def validate_field(data: Dict[str, Any], field: str, required: bool = True) -> ChainValidator:
        """验证字段并返回链式验证器"""
        value = data.get(field)
        validator = ChainValidator(value, field)
        if required:
            validator.required()
        return validator

    @staticmethod
    def extract_fields(data: Dict[str, Any], *fields: str) -> Dict[str, Any]:
        """提取多个字段并验证"""
        result = {}
        for field in fields:
            validator = RequestValidator.validate_field(data, field)
            result[field] = validator.raise_if_invalid()
        return result


def validate_request(*required_fields: str):
    """装饰器：自动验证请求数据和必需字段"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            try:
                # 获取并验证JSON数据
                data = RequestValidator.get_json_or_fail()
                
                # 验证必需字段
                validated_data = RequestValidator.extract_fields(data, *required_fields)
                
                # 将验证后的数据传递给原函数
                return f(data=data, validated=validated_data, *args, **kwargs)
                
            except ValidationError as e:
                return e.to_response()
                
        return decorated_function
    return decorator


def with_database_doc(collection_name: str, query_field: str, 
                     doc_name: str = "doc", 
                     error_message: Optional[str] = None):
    """装饰器：自动查询数据库文档"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            try:
                # 从kwargs中获取验证后的数据
                validated = kwargs.get('validated', {})
                query_value = validated.get(query_field)
                
                if not query_value:
                    raise ValidationError(f"缺少查询字段: {query_field}", "MISSING_QUERY_FIELD")
                
                # 这里需要从全局获取数据库连接
                # 实际使用时需要根据项目结构调整
                from app import mongo_db
                collection = mongo_db[collection_name]
                helper = DatabaseHelper(collection)
                
                # 构建查询
                if query_field.endswith('_ID') or query_field.endswith('_id'):
                    query = {"_id": query_value}
                else:
                    query = {"ID": query_value}
                
                # 查询文档
                doc = helper.find_one_or_fail(
                    query, 
                    error_message or f"{doc_name}不存在",
                    f"{doc_name.upper()}_NOT_FOUND"
                )
                
                # 将文档添加到kwargs
                kwargs[doc_name] = doc
                
                return f(*args, **kwargs)
                
            except ValidationError as e:
                return e.to_response()
                
        return decorated_function
    return decorator


# 便捷函数
def validate(value: Any, context: str = "") -> ChainValidator:
    """创建链式验证器的便捷函数"""
    return ChainValidator(value, context)


def db_helper(collection: Collection) -> DatabaseHelper:
    """创建数据库助手的便捷函数"""
    return DatabaseHelper(collection)
