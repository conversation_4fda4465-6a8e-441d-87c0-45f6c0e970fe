from flask import Blueprint, request, jsonify
from ...config import DatabaseConfig
from ...app import app
from ...utils import handle_api_exceptions, handle_database_exceptions, ApiValidator  # type: ignore
import sys
from typing import Any, Dict, Optional
from pymongo.collection import Collection
from flask_jwt_extended import jwt_required  # type: ignore
from ...type import ApiResponse, DataType, SceneInfo

data_analysis = Blueprint("data_analysis", __name__)

# 使用配置类
db_config = DatabaseConfig()
mongo_db = db_config.init_app(app)

# 检查数据库连接是否成功
if mongo_db is None:
    print("错误：数据库连接失败，程序退出")
    sys.exit(1)

db_base_data_radar: Collection[Dict[str, Any]] = mongo_db["radar"]
db_base_data_scene: Collection[Dict[str, Any]] = mongo_db["scene"]


# 获取雷达场景坐标
@data_analysis.route("/get_scene_coordinates", methods=["POST"])
@jwt_required()
@handle_api_exceptions(info="获取场景坐标")
@handle_database_exceptions
def get_scene_coordinates() -> ApiResponse:
    """获取场景坐标"""
    data: Optional[DataType] = request.get_json()
    if error := ApiValidator.validate_request_data(data):
        return error
    assert data is not None

    scene_id: Optional[str] = data.get("scene_ID")
    if error := ApiValidator.validate_id(scene_id, "scene_ID"):
        return error
    assert scene_id is not None

    db_scene_doc: Optional[SceneInfo] = db_base_data_scene.find_one({"_id": scene_id})
    if db_scene_doc is None:
        return jsonify({"error": "场景不存在", "code": "SCENE_NOT_FOUND"}), 404

    send_data = db_scene_doc.get("coordinates")
    if send_data is None:
        return (
            jsonify({"status": "error", "message": "场景坐标不存在", "data": []}),
            404,
        )

    if len(send_data) == 0:
        return (
            jsonify({"status": "error", "message": "雷达未设置场景坐标", "data": []}),
            404,
        )

    return (
        jsonify(
            {"status": "success", "message": "雷达场景坐标加载成功", "data": send_data}
        ),
        200,
    )


@data_analysis.route("/get_radar_host", methods=["POST"])
@jwt_required()
@handle_api_exceptions(info="获取雷达主机")
@handle_database_exceptions
def web_get_radar_host() -> ApiResponse:
    data = request.get_json()
    if (error := ApiValidator.validate_request_data(data)) is not None:
        return error
    assert data is not None

    radar_id = data.get("radar_ID", "")
    if (error := ApiValidator.validate_id(radar_id, "radar_ID")) is not None:
        return error
    assert radar_id is not None

    db_radar_doc = db_base_data_radar.find_one({"ID": radar_id})
    if db_radar_doc is None:
        return jsonify({"error": "雷达不存在", "code": "RADAR_NOT_FOUND"}), 404

    send_data = db_radar_doc.get("coordinates", {})
    if len(send_data) == 0:
        return jsonify({"status": "warning", "": "雷达未设置坐标", "data": []}), 500

    return (
        jsonify(
            {"status": "success", "message": "雷达坐标加载成功", "data": send_data}
        ),
        200,
    )


@data_analysis.route("/get_lastest_Img", methods=["POST"])
@jwt_required()
@handle_api_exceptions(info="获取最新图像")
@handle_database_exceptions
def web_lastest_Img() -> ApiResponse:
    data = request.get_json()  # 获取前端提交的 JSON 数据
    if (error := ApiValidator.validate_request_data(data)) is not None:
        return error
    assert data is not None

    # print(data)
    radar_ID = data.get("radar_ID", "")
    if (error := ApiValidator.validate_id(radar_ID, "radar_ID")) is not None:
        return error
    assert radar_ID is not None

    mission_ID = data.get("mission_ID", "")
    if (error := ApiValidator.validate_id(mission_ID, "mission_ID")) is not None:
        return error
    assert mission_ID is not None

    # 使用专有雷达类
    db_config = DatabaseConfig(db_name=radar_ID)
    db_this_radar = db_config.init_app(app)

    db_img_data = db_this_radar["img_data_" + mission_ID]

    base_data_doc = db_img_data.find_one(
        {"任务ID": int(mission_ID)}, sort=[("时间戳", -1)]
    )
    if base_data_doc is None:
        return jsonify({"status": "error", "message": "获取错误"}), 500

    file_path = base_data_doc.get("road_xy", "")
    if file_path is None:
        return jsonify({"status": "error", "message": "获取错误"}), 500

    print("http://127.0.0.1:5000/" + file_path)

    return (
        jsonify({"status": "success", "message": "http://127.0.0.1:5000/" + file_path}),
        200,
    )


# 获取最新形变图像URL
@data_analysis.route("/get_history_Img", methods=["POST"])
@jwt_required()
@handle_api_exceptions(info="获取历史图像")
@handle_database_exceptions
def web_history_Img():
    data: Optional[DataType] = request.get_json()  # 获取前端提交的 JSON 数据
    if (error := ApiValidator.validate_request_data(data)) is not None:
        return error
    assert data is not None

    radar_ID = data["radar_ID"]
    mission_ID = data["mission_ID"]
    begin_time = data["begin_time"]
    end_time = data["end_time"]
    db_this_radar = client[radar_ID]
    db_img_data = db_this_radar["img_data_" + mission_ID]
    end_time = datetime.strptime(
        end_time, "%Y-%m-%d %H:%M"
    ).timestamp()  # 解析为 datetime 对象,后转为时间戳
    print(end_time)
    base_data_doc = db_img_data.find_one(
        {"任务ID": int(mission_ID), "时间戳": {"$lte": end_time + 1}},
        sort=[("时间戳", -1)],
    )
    print(base_data_doc)
    file_path = base_data_doc["road_xy"]
    print("http://127.0.0.1:5000/" + file_path)
    return jsonify(
        {
            "status": "success",
            "message": {"img_url": "http://127.0.0.1:5000/" + file_path},
        }
    )
