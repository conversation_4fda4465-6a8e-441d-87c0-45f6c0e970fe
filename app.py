from flask import Flask, request, jsonify
from flask_pymongo import PyMongo
from flask_cors import CORS
from pymongo.database import Database
from pymongo.collection import Collection
from typing import Dict, List, Any, Optional, Union
import logging
import sys
from flask_jwt_extended import jwt_required, get_jwt_identity, J<PERSON>TManager
from datetime import timedelta
from pymongo.errors import (
    ConnectionFailure,
    ServerSelectionTimeoutError,
    OperationFailure,
    InvalidDocument,
    DocumentTooLarge,
)

# 本地模块导入
from my_code.web_code.user import user
from my_code.web_code.radar_manage import radar_manage
from my_code.web_code.data_analysis import data_analysis
from my_code.web_code.radar_information import radar_information
from my_code.web_code.scene_parameter import scene_parameter


class DatabaseConfig:
    def __init__(
        self, mongo_uri: str = "mongodb://localhost:27017/", db_name: str = "base_data"
    ):
        """
        初始化业务数据库连接配置

        属性:
            mongo_uri (str): MongoDB连接URI，默认为本地27017端口
            db_name (str): 业务数据库名称，默认为'base_data'
        """
        self.mongo_uri: str = mongo_uri
        self.db_name: str = db_name

    def init_app(self, app: Flask) -> Optional[Database[Dict[str, Any]]]:
        """
        初始化Flask应用并连接MongoDB数据库

        Args:
            app (Flask): Flask应用实例

        Returns:
            Optional[Database[Dict[str, Any]]]: 成功返回MongoDB数据库连接对象，失败返回None

        Raises:
            Exception: 捕获并打印数据库连接过程中的异常
        """
        try:
            # Flask-PyMongo 用于认证
            app.config["MONGO_URI"] = self.mongo_uri + self.db_name
            mongo = PyMongo(app)

            # 检查 Flask-PyMongo 连接
            if not hasattr(mongo, "db") or mongo.db is None:
                print("错误：Flask-PyMongo 连接失败")
                return None

            # 测试连接
            try:
                mongo.db.list_collection_names()
            except Exception as e:
                print(f"数据库连接测试失败: {e}")
                return None

            return mongo.db

        except Exception as e:
            print(f"数据库初始化失败: {e}")
            return None


# 配置日志
logger = logging.getLogger(__name__)

app = Flask(__name__)
app.config["DEBUG"] = True
app.secret_key = "secret"
CORS(app)

# JWT配置
app.config["JWT_SECRET_KEY"] = "super-secret"  # TODO: 生产环境需要更改为安全的密钥
app.config["JWT_ACCESS_TOKEN_EXPIRES"] = timedelta(days=1)  # Access Token有效期：1天
app.config["JWT_REFRESH_TOKEN_EXPIRES"] = timedelta(
    days=30
)  # Refresh Token有效期：30天
jwt = JWTManager(app)

# 注册蓝图
app.register_blueprint(user, url_prefix="/user")  # 用户管理模块
app.register_blueprint(radar_manage, url_prefix="/radar_manage")  # 雷达管理模块
app.register_blueprint(data_analysis, url_prefix="/data_analysis")  # 数据分析模块
app.register_blueprint(
    radar_information, url_prefix="/radar_information"
)  # 雷达信息模块
app.register_blueprint(scene_parameter, url_prefix="/scene_parameter")  # 场景参数模块

# 使用配置类
db_config = DatabaseConfig()
mongo_db = db_config.init_app(app)

# 检查数据库连接是否成功
if mongo_db is None:
    print("错误：数据库连接失败，程序退出")
    sys.exit(1)


# 业务相关 - 使用类型断言确保类型正确
db_base_data_radar: Collection[Dict[str, Any]] = mongo_db["radar"]
db_base_data_user: Collection[Dict[str, Any]] = mongo_db["users"]
db_base_data_scene: Collection[Dict[str, Any]] = mongo_db["scene"]

print("数据库连接成功")
print(f"雷达数据集合: {db_base_data_radar.name}")
print(f"用户数据集合: {db_base_data_user.name}")
print(f"场景数据集合: {db_base_data_scene.name}")


# 类型定义
RadarInfo = Dict[str, Any]
SceneInfo = Dict[str, Any]
ApiResponse = Union[Dict[str, Any], tuple[Dict[str, Any], int]]


@app.route("/check_all_radar", methods=["GET", "POST"])
@jwt_required()
def web_check_all_radar() -> ApiResponse:
    """
    获取指定场景下的所有雷达信息

    Returns:
        ApiResponse: 成功返回雷达列表，失败返回错误信息和状态码
    """
    try:
        # 获取当前用户身份（可选，用于日志记录）
        current_user: Optional[str] = get_jwt_identity()

        # 验证请求数据
        data: Optional[Dict[str, Any]] = request.get_json()
        if not data:
            return {"error": "请求数据为空", "code": "INVALID_REQUEST"}, 400

        scene_id: Optional[str] = data.get("scene_ID")
        if not scene_id:
            return {"error": "scene_ID参数缺失", "code": "MISSING_SCENE_ID"}, 400

        # 简化验证逻辑，直接检查是否为空字符串
        if not scene_id.strip():
            return {"error": "scene_ID不能为空", "code": "INVALID_SCENE_ID"}, 400

        # 获取 MongoDB 实例和集合
        mongo: PyMongo = current_app.extensions["pymongo"]
        scene_collection: Collection = mongo.db.base_data_scene
        radar_collection: Collection = mongo.db.base_data_radar

        # 查询场景信息
        db_scene: Optional[SceneInfo] = scene_collection.find_one({"_id": scene_id})

        if not db_scene:
            if current_user:
                logger.warning(f"Scene not found: {scene_id}, user: {current_user}")
            return {"error": "场景不存在", "code": "SCENE_NOT_FOUND"}, 404

        # 获取雷达ID列表，确保类型安全
        radar_ids_raw: Any = db_scene.get("radar_ID", [])
        if not isinstance(radar_ids_raw, list):
            logger.error(
                f"Invalid radar_ID format in scene {scene_id}: {type(radar_ids_raw)}"
            )
            return {"error": "场景数据格式错误", "code": "INVALID_SCENE_DATA"}, 500

        radar_ids: List[str] = [str(rid) for rid in radar_ids_raw if rid]

        if not radar_ids:
            return {"data": [], "message": "该场景下暂无雷达设备"}, 200

        # 批量查询雷达信息
        radar_cursor: Cursor = radar_collection.find(
            {"ID": {"$in": radar_ids}},
            {"ID": 1, "name": 1, "_id": 0},  # 只返回需要的字段
        )

        # 将查询结果转换为列表
        found_radars: List[RadarInfo] = list(radar_cursor)

        # 创建 ID 到雷达信息的映射，便于查找
        radar_map: Dict[str, RadarInfo] = {
            str(radar["ID"]): radar for radar in found_radars
        }

        # 按原始顺序构建结果列表，处理缺失的雷达
        radar_list: List[Dict[str, Any]] = []
        for radar_id in radar_ids:
            radar_id_str = str(radar_id)
            if radar_id_str in radar_map:
                radar_info = radar_map[radar_id_str]
                radar_list.append(
                    {
                        "ID": radar_info["ID"],
                        "name": radar_info.get("name", "未命名雷达"),
                    }
                )
            else:
                # 记录缺失的雷达ID
                logger.warning(f"Radar not found: {radar_id_str} in scene: {scene_id}")
                radar_list.append(
                    {"ID": radar_id_str, "name": "雷达信息缺失", "status": "missing"}
                )

        return {
            "data": radar_list,
            "total": len(radar_list),
            "scene_id": scene_id,
            "message": "查询成功",
        }

    except ConnectionFailure as e:
        logger.error(f"数据库连接失败: {str(e)}")
        return {"error": "数据库连接失败", "code": "DB_CONNECTION_ERROR"}, 503

    except OperationFailure as e:
        logger.error(f"数据库操作失败: {str(e)}")
        return {"error": "数据库操作失败", "code": "DB_OPERATION_ERROR"}, 500

    except InvalidDocument as e:
        logger.error(f"无效文档格式: {str(e)}")
        return {"error": "数据格式错误", "code": "INVALID_DOCUMENT"}, 400

    except ValueError as e:
        logger.error(f"参数值错误: {str(e)}")
        return {"error": "参数格式错误", "code": "INVALID_PARAMETER"}, 400

    except Exception as e:
        logger.error(f"未预期的错误: {str(e)}", exc_info=True)
        return {"error": "服务器内部错误", "code": "INTERNAL_ERROR"}, 500


# 运行测试
if __name__ == "__main__":
    app.run()
