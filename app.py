from flask import Flask, request, jsonify, send_file, send_from_directory
from flask_cors import CORS
from pymongo.collection import Collection
from typing import Dict, List, Any, Optional
import logging
import sys
import os
import json
from flask_jwt_extended import jwt_required, get_jwt_identity, JWTManager  # type: ignore
from config import config_by_name, DatabaseConfig  # type: ignore

# 本地模块导入
from my_code.web_code.user import user
from my_code.web_code.radar_manage import radar_manage
from my_code.web_code.data_analysis import data_analysis
from my_code.web_code.radar_information import radar_information
from my_code.web_code.scene_parameter import scene_parameter
from utils import handle_api_exceptions, handle_database_exceptions, ApiValidator  # type: ignore
from type import RadarInfo, SceneInfo, RadarSate, ApiResponse, DataType


# 配置日志
logger = logging.getLogger(__name__)

app = Flask(__name__)
app.config["DEBUG"] = True
app.secret_key = "secret"
CORS(app)

# 1. 加载配置
config_name = os.getenv("FLASK_ENV", "development").lower()
app_config = config_by_name.get(config_name, config_by_name["default"])
app.config.from_object(app_config)
jwt = JWTManager(app)

# 注册蓝图
app.register_blueprint(user, url_prefix="/user")  # 用户管理模块
app.register_blueprint(radar_manage, url_prefix="/radar_manage")  # 雷达管理模块
app.register_blueprint(data_analysis, url_prefix="/data_analysis")  # 数据分析模块
app.register_blueprint(
    radar_information, url_prefix="/radar_information"
)  # 雷达信息模块
app.register_blueprint(scene_parameter, url_prefix="/scene_parameter")  # 场景参数模块

# 使用配置类
db_config = DatabaseConfig()
mongo_db = db_config.init_app(app)

# 检查数据库连接是否成功
if mongo_db is None:
    print("错误：数据库连接失败，程序退出")
    sys.exit(1)


# 业务相关 - 使用类型断言确保类型正确
db_base_data_radar: Collection[Dict[str, Any]] = mongo_db["radar"]
db_base_data_user: Collection[Dict[str, Any]] = mongo_db["users"]
db_base_data_scene: Collection[Dict[str, Any]] = mongo_db["scene"]


logger.info("数据库连接成功")  # 使用 logger
logger.info(f"雷达数据集合: {db_base_data_radar.name}")
logger.info(f"用户数据集合: {db_base_data_user.name}")
logger.info(f"场景数据集合: {db_base_data_scene.name}")


@app.route("/check_all_radar", methods=["GET", "POST"])
@jwt_required()
@handle_database_exceptions
@handle_api_exceptions(info="获取指定场景下的所有雷达信息")
def web_check_all_radar() -> ApiResponse:
    """
    获取指定场景下的所有雷达信息

    Returns:
        ApiResponse: 成功返回雷达列表，失败返回错误信息和状态码
    """

    # 验证请求数据
    data: Optional[DataType] = request.get_json()
    if error := ApiValidator.validate_request_data(data):
        return error
    assert data is not None

    # 获取场景ID
    scene_id: Optional[str] = data.get("scene_ID")
    if error := ApiValidator.validate_id(scene_id, "scene_ID"):
        return error

    # 查询场景信息
    db_scene_doc: Optional[SceneInfo] = db_base_data_scene.find_one({"_id": scene_id})
    if db_scene_doc is None:
        return jsonify({"error": "场景不存在", "code": "SCENE_NOT_FOUND"}), 404

    # 获取雷达ID列表，确保类型安全
    radar_id: List[str] = db_scene_doc.get("radar_ID", [])

    radar_list: List[Dict[str, str]] = []
    for each_id in radar_id:
        db_each_radar_doc: Optional[RadarInfo] = db_base_data_radar.find_one(
            {"ID": each_id}
        )
        if db_each_radar_doc is None:
            logger.warning(f"Radar not found: {each_id} in scene: {scene_id}")
            continue
        radar_list.append({"ID": each_id, "name": db_each_radar_doc.get("name", "")})

    if not radar_list:
        return jsonify({"data": [], "message": "该场景下暂无雷达设备"}), 200

    # 统一使用jsonify返回
    return jsonify({"data": radar_list}), 200


@app.route("/listen_radar_state", methods=["POST"])
@jwt_required()
@handle_api_exceptions(info="监听指定雷达的工作状态")
@handle_database_exceptions
def web_listen_radar_state() -> ApiResponse:
    """监听指定雷达的工作状态"""
    # 验证请求数据
    data: Optional[DataType] = request.get_json()
    if error := ApiValidator.validate_request_data(data):
        return error
    assert data is not None

    radar_id = data.get("radar_ID", "")
    if error := ApiValidator.validate_id(radar_id, "radar_ID"):
        return error

    radar_state_doc: Optional[RadarSate] = db_base_data_radar.find_one({"ID": radar_id})
    if radar_state_doc is None:
        return jsonify({"error": "雷达不存在", "code": "RADAR_NOT_FOUND"}), 404

    radar_state = {
        "is_work": (str(radar_state_doc.get("is_work", ""))),
        "state": (
            "work"
            if radar_state_doc.get("is_work", "") == 1
            else "online" if radar_state_doc["is_online"] == 1 else "offline"
        ),
    }
    return jsonify(radar_state), 200


# 图像和文件处理路由
@app.route("/<radar_id>/work_data/<mission_id>/image_data/<filename>")
@handle_api_exceptions(info="获取图像文件")
def get_image(radar_id: str, mission_id: str, filename: str) -> ApiResponse:
    """获取指定雷达任务的图像数据"""
    file_path = f"./{radar_id}/work_data/{mission_id}/image_data/{filename}"
    return send_file(file_path, mimetype="image/png"), 200


@app.route("/download/<path:file_path>", methods=["POST", "GET"])
@handle_api_exceptions(info="文件下载")
def web_download(file_path: str) -> ApiResponse:
    """下载指定路径的文件"""
    normalized_path = os.path.normpath(file_path)
    ALLOWED_BASE_DIR = r"E:/实验室/云平台/react_app/public/source"
    absolute_path = os.path.abspath(os.path.join(ALLOWED_BASE_DIR, normalized_path))
    print("访问文件地址为：", absolute_path)

    if not os.path.exists(absolute_path):
        return jsonify({"status": "error", "message": "文件夹不存在"}), 500
    if not os.path.isfile(absolute_path):
        return jsonify({"status": "error", "message": "文件不存在"}), 500

    return (
        send_file(
            absolute_path,
            as_attachment=True,
            download_name=os.path.basename(absolute_path),
        ),
        200,
    )


@app.route("/get_scene_list", methods=["POST"])
@jwt_required()
@handle_database_exceptions
@handle_api_exceptions(info="获取当前用户可访问的场景列表")
def web_get_scene_list() -> ApiResponse:
    """获取当前用户可访问的场景列表"""
    identity = get_jwt_identity()
    identity_dict = json.loads(identity)
    user_name = identity_dict.get("username", "")
    db_user_doc = db_base_data_user.find_one({"username": user_name})
    if not db_user_doc:
        return jsonify({"status": "error", "message": "用户不存在"}), 404

    scene_id = db_user_doc.get("scene_ID", "")

    scene_id_list: List[Dict[str, str]] = []
    for each_scene in scene_id:
        result = db_base_data_scene.find_one({"_id": each_scene})
        if not result:
            logger.warning(f"{user_name}的场景不存在: {each_scene}")
            continue

        scene_id_list.append(
            {
                "name": result["name"],
                "background_pack": result["background_pack"],
                "ID": each_scene,
                "coordinates": result["coordinates"],
            }
        )
    return (
        jsonify(
            {"states": "success", "message": "成功获取场景信息", "data": scene_id_list}
        ),
        200,
    )


@app.route("/image/<path:filename>", methods=["POST", "GET"])
@handle_api_exceptions(info="获取静态图像文件")
def get_image1(filename: str) -> ApiResponse:
    """获取静态图像文件"""
    return send_from_directory("", filename), 200


# 运行测试
if __name__ == "__main__":
    app.run()
