"""
雷达信息模块
提供雷达设备的详细信息、状态监控、历史记录等功能
"""

from flask import Blueprint, request, jsonify, current_app
from functools import wraps
import jwt
from datetime import datetime, timedelta
from bson import ObjectId

# 创建蓝图
radar_information = Blueprint("radar_information", __name__)


# JWT认证装饰器
def token_required(f):
    @wraps(f)
    def decorated(*args, **kwargs):
        token = None
        if "Authorization" in request.headers:
            token = request.headers["Authorization"].split(" ")[1]

        if not token:
            return jsonify({"message": "缺少认证令牌！"}), 401

        try:
            data = jwt.decode(
                token, current_app.config["SECRET_KEY"], algorithms=["HS256"]
            )
            current_user = current_app.mongo.db.users.find_one(
                {"username": data["username"]}
            )
        except:
            return jsonify({"message": "令牌无效！"}), 401

        return f(current_user, *args, **kwargs)

    return decorated


# 获取雷达详细信息
@radar_information.route("/info/<radar_id>", methods=["GET"])
@token_required
def get_radar_info(current_user, radar_id):
    try:
        # 获取雷达基本信息
        radar = current_app.mongo.db.radars.find_one({"_id": ObjectId(radar_id)})

        if not radar:
            return jsonify({"message": "雷达设备不存在！"}), 404

        # 转换ObjectId为字符串
        radar["_id"] = str(radar["_id"])

        # 获取雷达最新状态
        latest_status = current_app.mongo.db.radar_status.find_one(
            {"radar_id": ObjectId(radar_id)}, sort=[("timestamp", -1)]
        )

        if latest_status:
            latest_status["_id"] = str(latest_status["_id"])
            latest_status["radar_id"] = str(latest_status["radar_id"])

        # 获取雷达维护记录
        maintenance_records = list(
            current_app.mongo.db.maintenance_records.find(
                {"radar_id": ObjectId(radar_id)}, sort=[("timestamp", -1)], limit=5
            )
        )

        for record in maintenance_records:
            record["_id"] = str(record["_id"])
            record["radar_id"] = str(record["radar_id"])

        # 组合完整信息
        radar_info = {
            "basic_info": radar,
            "current_status": latest_status,
            "recent_maintenance": maintenance_records,
        }

        return jsonify(radar_info)
    except:
        return jsonify({"message": "获取雷达信息失败！"}), 500


# 获取雷达状态历史
@radar_information.route("/status-history/<radar_id>", methods=["GET"])
@token_required
def get_status_history(current_user, radar_id):
    try:
        # 获取时间范围参数
        start_date = request.args.get("start_date")
        end_date = request.args.get("end_date")
        limit = int(request.args.get("limit", 100))

        # 构建查询条件
        query = {"radar_id": ObjectId(radar_id)}
        if start_date and end_date:
            query["timestamp"] = {
                "$gte": datetime.fromisoformat(start_date),
                "$lte": datetime.fromisoformat(end_date),
            }

        # 获取状态历史
        status_history = list(
            current_app.mongo.db.radar_status.find(
                query, sort=[("timestamp", -1)], limit=limit
            )
        )

        # 转换ObjectId为字符串
        for status in status_history:
            status["_id"] = str(status["_id"])
            status["radar_id"] = str(status["radar_id"])

        return jsonify(status_history)
    except:
        return jsonify({"message": "获取状态历史失败！"}), 500


# 获取雷达参数配置
@radar_information.route("/parameters/<radar_id>", methods=["GET"])
@token_required
def get_radar_parameters(current_user, radar_id):
    try:
        # 获取雷达参数配置
        radar = current_app.mongo.db.radars.find_one(
            {"_id": ObjectId(radar_id)}, {"parameters": 1}
        )

        if not radar:
            return jsonify({"message": "雷达设备不存在！"}), 404

        return jsonify(radar.get("parameters", {}))
    except:
        return jsonify({"message": "获取雷达参数失败！"}), 500


# 获取雷达维护记录
@radar_information.route("/maintenance/<radar_id>", methods=["GET"])
@token_required
def get_maintenance_records(current_user, radar_id):
    try:
        # 获取分页参数
        page = int(request.args.get("page", 1))
        per_page = int(request.args.get("per_page", 10))

        # 计算跳过的记录数
        skip = (page - 1) * per_page

        # 获取维护记录
        records = list(
            current_app.mongo.db.maintenance_records.find(
                {"radar_id": ObjectId(radar_id)},
                sort=[("timestamp", -1)],
                skip=skip,
                limit=per_page,
            )
        )

        # 转换ObjectId为字符串
        for record in records:
            record["_id"] = str(record["_id"])
            record["radar_id"] = str(record["radar_id"])

        # 获取总记录数
        total_records = current_app.mongo.db.maintenance_records.count_documents(
            {"radar_id": ObjectId(radar_id)}
        )

        return jsonify(
            {
                "records": records,
                "pagination": {
                    "page": page,
                    "per_page": per_page,
                    "total_records": total_records,
                    "total_pages": (total_records + per_page - 1) // per_page,
                },
            }
        )
    except:
        return jsonify({"message": "获取维护记录失败！"}), 500


# 添加维护记录
@radar_information.route("/maintenance/<radar_id>", methods=["POST"])
@token_required
def add_maintenance_record(current_user, radar_id):
    try:
        data = request.json

        if not data:
            return jsonify({"message": "没有提供维护记录信息！"}), 400

        # 验证必要字段
        required_fields = ["maintenance_type", "description"]
        for field in required_fields:
            if field not in data:
                return jsonify({"message": f"缺少必要字段: {field}"}), 400

        # 创建维护记录
        new_record = {
            "radar_id": ObjectId(radar_id),
            "maintenance_type": data.get("maintenance_type"),
            "description": data.get("description"),
            "performed_by": current_user.get("username"),
            "timestamp": datetime.utcnow(),
            "details": data.get("details", {}),
            "status": data.get("status", "completed"),
        }

        result = current_app.mongo.db.maintenance_records.insert_one(new_record)

        return (
            jsonify(
                {"message": "维护记录添加成功！", "record_id": str(result.inserted_id)}
            ),
            201,
        )
    except:
        return jsonify({"message": "添加维护记录失败！"}), 500


# 获取雷达操作日志
@radar_information.route("/logs/<radar_id>", methods=["GET"])
@token_required
def get_radar_logs(current_user, radar_id):
    try:
        # 获取时间范围和分页参数
        start_date = request.args.get("start_date")
        end_date = request.args.get("end_date")
        page = int(request.args.get("page", 1))
        per_page = int(request.args.get("per_page", 20))

        # 构建查询条件
        query = {"radar_id": ObjectId(radar_id)}
        if start_date and end_date:
            query["timestamp"] = {
                "$gte": datetime.fromisoformat(start_date),
                "$lte": datetime.fromisoformat(end_date),
            }

        # 计算跳过的记录数
        skip = (page - 1) * per_page

        # 获取操作日志
        logs = list(
            current_app.mongo.db.radar_logs.find(
                query, sort=[("timestamp", -1)], skip=skip, limit=per_page
            )
        )

        # 转换ObjectId为字符串
        for log in logs:
            log["_id"] = str(log["_id"])
            log["radar_id"] = str(log["radar_id"])

        # 获取总记录数
        total_logs = current_app.mongo.db.radar_logs.count_documents(query)

        return jsonify(
            {
                "logs": logs,
                "pagination": {
                    "page": page,
                    "per_page": per_page,
                    "total_records": total_logs,
                    "total_pages": (total_logs + per_page - 1) // per_page,
                },
            }
        )
    except:
        return jsonify({"message": "获取操作日志失败！"}), 500


# 错误处理
@radar_information.errorhandler(404)
def not_found(error):
    return jsonify({"message": "资源不存在！"}), 404


@radar_information.errorhandler(500)
def internal_error(error):
    return jsonify({"message": "服务器内部错误！"}), 500
